import { BookPurchaseModalComponent } from '@/components/book-purchase-modal/book-purchase-modal.component'
import { BreadcrumbComponent } from '@/components/breadcrumb/breadcrumb.component'
import { LoadingIndicatorComponent } from '@/components/loading-indicator/loading-indicator.component'
import { FavoritesIconComponent } from "@/components/svg-components/favorites-icon/favorites-icon.component"
import { ClickOutsideDirective } from '@/directives/clickOutside'
import { environment } from "@/env/environment"
import { Track } from '@/interfaces/track'
import { AdvertisingService } from "@/services/advertising.service"
import { AuthService } from '@/services/auth.service'
import { LibraryService } from "@/services/library.service"
import { ProfileService } from "@/services/profile.service"
import { ShareDataService } from "@/services/share-data.service"
import { ToasterService } from '@/services/toaster.service'
import { CommonModule, isPlatformBrowser, isPlatformServer, NgOptimizedImage } from "@angular/common"
import { Component, DestroyRef, effect, ElementRef, HostListener, inject, OnD<PERSON>roy, PLATFORM_ID, signal, ViewChild } from '@angular/core'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { FormsModule } from "@angular/forms"
import { DomSanitizer, Meta, Title } from "@angular/platform-browser"
import { ActivatedRoute, Router } from "@angular/router"
import { TranslocoModule, TranslocoService } from "@jsverse/transloco"
import { TextInteractionComponent } from '../content/text-interaction/text-interaction.component'

@Component({
  selector: 'app-library',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    BreadcrumbComponent,
    NgOptimizedImage,
    TextInteractionComponent,
    TranslocoModule,
    ClickOutsideDirective,
    LoadingIndicatorComponent,
    FavoritesIconComponent,
    BookPurchaseModalComponent
],
  templateUrl: './library.component.html',
  styleUrl: './library.component.scss'
})
export class LibraryComponent implements OnDestroy {
  shareDataService = inject(ShareDataService)
  profileService = inject(ProfileService);
  libraryService = inject(LibraryService);
  authService = inject(AuthService);
  route = inject(ActivatedRoute);
  router = inject(Router);
  platformId = inject(PLATFORM_ID)
  titleService = inject(Title)
  metaService = inject(Meta);
  advertisingService = inject(AdvertisingService);
  translocoService = inject(TranslocoService);
  auth = inject(AuthService)
  sanitizer = inject(DomSanitizer)
  code = this.route.snapshot.paramMap.get('code');
  selection: any = signal(null)
  toasterService = inject(ToasterService);
  quote: string | null = null
  id: number | null = null
  rendition: any = null
  data: any = null
  currentPage: any
  tabs = ['О книге', 'Читать', 'Аудиокнига']
  tab = 'О книге'
  descriptionTab = 'annotation'
  bookLoaded = false;
  bookId = ""
  private readonly destroyRef = inject(DestroyRef);
  chapter = 0
  theme = 'light'
  fontSize = '17'
  fontFamily = 'Arial'
  audioBookIndex = -1;
  chapterContent: any = ''
  chapterCount: number = 0
  chapterTitles: any = []
  chapterReadingTime: number = 0

  likesContent: any = [];
  favouriteContent: any = [];
  likesCount: number = 0;
  lang = 'ru';
  relatedBooksShowLast: boolean = false;
  relatedBooks = [
    {
      name: 'Панчадаши',
      author: 'Шри Видьяранья Свами',
      bookImgSrc: 'assets/images/book_1.webp'
    },
    {
      name: 'Аватары Господа Шивы',
      author: 'Всемирная Община Санатана Дхармы',
      bookImgSrc: 'assets/images/book_2.webp'
    },
    {
      name: 'Сборник лекций по Шайва-сиддханте',
      author: 'Шри Гуру Свами Вишнудевананда Гири',
      bookImgSrc: 'assets/images/book_3.webp'
    },
  ];
  fullscreenBookTheme = 'light';
  fullscreenBookFont = 'Prata';
  fullscreenBookFontSize: number = 24; // Will be set to responsive default in constructor
  fullscreenBookTextWidth = 'narrow'; // Default to narrow column (700px)
  isFullscreenBookDialogActive: boolean = false;
  showFullscreenOptions: boolean = false;
  similar: any = []
  contents: boolean = false;
  fullscreenContents: boolean = false;
  durationMinutes: any = ''
  // private touchStartX: number = 0;
  // private touchEndX: number = 0;
  // private mouseStartX: number = 0;
  // private mouseDown: boolean = false;
  // private readonly swipeThreshold: number = 30;

  quoteId = this.route.snapshot.queryParams['quoteId']
  libraryQuote: any = null
  paymentType: string = 'stripe'
  showPurchaseModal: boolean = false

  constructor() {
    // Set responsive default font size
    this.fullscreenBookFontSize = this.getResponsiveDefaultFontSize();

    // Load saved fullscreen settings for guest initially
    this.loadFullscreenSettings();

    // Add window resize listener to update font size responsively
    if (isPlatformBrowser(this.platformId)) {
      window.addEventListener('resize', () => {
        // Only update to responsive default if user hasn't manually changed font size
        const currentDefault = this.getResponsiveDefaultFontSize();
        if (this.fullscreenBookFontSize === 24 || this.fullscreenBookFontSize === 18) {
          this.fullscreenBookFontSize = currentDefault;
          this.applyFullscreenFontSize();
        }
      });
    }

    effect(() => {
      if(this.profileService.name() && this.profileService.profile) {
        this.setCount();
        // Load user-specific fullscreen settings when profile becomes available
        this.loadFullscreenSettings();

        // Проверяем сохраненную позицию чтения после загрузки профиля
        if (this.tab === 'Читать' && this.data?.id) {
          const savedPosition = this.getSavedReadingPosition();
          if (savedPosition !== null && savedPosition.chapter !== this.chapter) {
            // Переходим на сохраненную позицию только если она отличается от текущей
            this.getChapter(savedPosition.chapter, savedPosition.scrollPosition);
          }
        }
      }
    });
  }

    get isMobile() {
      if (isPlatformServer(this.platformId)) {
        return false;
      }
  
      return window.innerWidth <= 768;
    }

  // Get responsive default font size based on screen width
  getResponsiveDefaultFontSize(): number {
    if (isPlatformBrowser(this.platformId)) {
      const screenWidth = window.innerWidth;
      if (screenWidth <= 768) {
        return 18; // Smaller font for tablet and mobile
      }
    }
    return 24; // Default large font for desktop
  }

  // Calculate proportional line-height based on font size
  calculateProportionalLineHeight(fontSize: number): number {
    return Math.round(fontSize * 1.5); // 1.5 ratio for optimal readability
  }

  ngOnInit() {
    if(this.authService.isAuth){
      this.libraryService.getLikes(this.route.snapshot.params['code']).subscribe((res: any) => {
        this.likesContent = res[0];
        this.likesCount = res[1];
      })
      this.libraryService.getFavourites().subscribe((res: any) => {
        // Проверяем, что res это массив или объект с массивом
        if (Array.isArray(res)) {
          this.favouriteContent = res.map((e: any) => e.id);
        } else if (res && Array.isArray(res.items)) {
          this.favouriteContent = res.items.map((e: any) => e.id);
        } else {
          this.favouriteContent = [];
        }
      })
    }

    // if(this.profileService.name() && this.profileService.profile) {
    //   this.setCount();
    // } else {
    //   this.profileService.setProfile();
    // }

    this.translocoService.langChanges$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((lang: any) => {
      if(this.lang === lang) {
        return;
      } else {
        this.lang = lang;
      }
      this.getDate();
    })

    this.route.params.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(params => {
      const newBookId = params['code'];
      if (newBookId !== this.bookId) {
        // Сбрасываем данные при смене книги
        this.similar = [];
        this.similarCall = false;
        this.tab = 'О книге'; // Возвращаемся на первый таб
        this.chapter = 0; // Сбрасываем главу

        if (isPlatformBrowser(this.platformId)) {
          window.scrollTo({ top: 0, behavior: 'smooth' });
        }
      }
      this.bookId = newBookId;
      this.getDate();
    })

    // Handle query parameters for tab selection
    this.route.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(queryParams => {
      const tabParam = queryParams['tab'];
      if (tabParam && this.tabs.includes(tabParam)) {
        this.tab = tabParam;

        // If switching to reading tab, load the first chapter
        if (tabParam === 'Читать') {
          if (this.quoteId) {
            this.libraryService.getQuote(this.quoteId).subscribe((res: any) => {
              this.libraryQuote = res;
              this.chapter = res.page;
              this.getChapter(res.page);
            })
          } else {
            // Проверяем сохраненную позицию чтения
            const savedPosition = this.getSavedReadingPosition();
            const startChapter = savedPosition !== null ? savedPosition.chapter : 0;
            this.getChapter(startChapter, savedPosition?.scrollPosition);
          }
        }
      }
    })
  }

  ngOnDestroy() {
    // Очищаем timeout при уничтожении компонента
    if (this.scrollSaveTimeout) {
      clearTimeout(this.scrollSaveTimeout);
    }
  }

  get duration() {
    if(!this.data || !this.data.duration) return 0
    const [hours, minutes, seconds] = this.data.duration.split(':').map(Number);
    const totalMinutes = hours * 60 + minutes + Math.round(seconds / 60);
    const formattedHours = Math.floor(totalMinutes / 60);
    const formattedMinutes = totalMinutes % 60;
    return `${formattedHours} ч. ${formattedMinutes} мин.`;
  }

  setCount() {
    this.likesContent = this.profileService.profile!.libraryLikes;
    this.favouriteContent = this.profileService.profile!.libraryFavourites;
  }

  getDate(views = true) {
    if(this.bookId)
    this.libraryService.get(this.bookId, views).subscribe((res: any) => {
      this.id = res.id
      this.titleService.setTitle(res.seo_title || res.title);
      this.metaService.updateTag({name: 'description', content: res.seo_description})
      this.data = res;

      // Безопасный расчет времени аудио
      if (this.data.audio && Array.isArray(this.data.audio) && this.data.audio.length > 0) {
        const totalSeconds = this.data.audio.reduce((a: number, b: any) => {
          const duration = Number(b.duration);
          return a + (isNaN(duration) ? 0 : duration);
        }, 0);
        this.durationMinutes = Math.ceil(totalSeconds / 60);
      } else {
        this.durationMinutes = 0;
      }

      // Обновляем массив табов в зависимости от наличия аудио
      this.updateTabs();

      // Handle tab selection from query parameters after tabs are updated
      const tabParam = this.route.snapshot.queryParams['tab'];
      if (tabParam && this.tabs.includes(tabParam)) {
        this.changeTab(tabParam);
      } else if(this.quoteId) {
        this.changeTab('Читать');
      }

      // this.data.audio.forEach((el: any) => {
      //   this.getAudioDuration(el.url).then(duration => {
      //     el.time = this.formatDuration(duration)
      //   })
      //   .catch(error => console.error(error));
      // });
    })
  }

  formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    return [
      hours > 0 ? String(hours).padStart(2, "0") : "00",
      String(minutes).padStart(2, "0"),
      String(secs).padStart(2, "0")
    ].join(":");
  }

  share(content: any) {
    if (isPlatformBrowser(this.platformId)) {
      const lang = this.translocoService.getActiveLang()
      navigator.clipboard.writeText(`${environment.baseUrl}/${lang}/library/${content.code}`).then(() =>
        this.toasterService.showToast('Ссылка на книгу скопирована в буфер обмена!', 'success', 'bottom-middle', 3000)
      )
    }
  }


  favorite(id: number) {
    if (!this.auth.token()) {
      this.shareDataService.showInfoModal('error');
      return
    }
    this.libraryService.addToFavourites(id).subscribe({
      next: (r) => {
        if(this.favouriteContent.includes(id)) {
          this.favouriteContent.splice(this.favouriteContent.indexOf(id), 1)
          this.toasterService.showToast('Книга удалена из избранного!', 'success', 'bottom-middle', 3000);
        } else {
          this.favouriteContent.push(id)
          this.toasterService.showToast('Книга добавлена в избранное!', 'success', 'bottom-middle', 3000);
        }
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    })
  }

  like(id: number) {
    if (!this.auth.token()) {
      this.shareDataService.showInfoModal('error');
      return;
    }

    this.libraryService.like(id).subscribe({
      next: (r) => {

        if(this.likesContent.includes(id)) {
          this.likesContent.splice(this.likesContent.indexOf(id), 1)
          this.likesCount--;
          this.toasterService.showToast('Книга удалена из понравившихся!', 'success', 'bottom-middle', 3000);
        } else {
          this.likesContent.push(id)
          this.likesCount++;
          this.toasterService.showToast('Книга добавлена в понравившееся!', 'success', 'bottom-middle', 3000);
        }

      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    });
  }

  get likes () {
    return this.likesContent.filter((item: any) => item.id === this.data.id);
  }

  changeFontSize(e: Event) {
    const target = e.target as HTMLInputElement;
    if (this.rendition) {
      // Create a style rule that targets all text elements
      const fontRule = `* { font-size: ${target.value}px !important; }`;

      // Register and apply new theme with our font size
      this.rendition.themes.register('custom-size', {
        body: {
          'font-size': `${target.value}px !important`,
        },
        'p, div, span, h1, h2, h3, h4, h5, h6': {
          'font-size': `${target.value}px !important`,
        }
      });

      // Add direct stylesheet injection
      const doc = this.rendition.getContents()[0].document;
      let style = doc.createElement('style');
      style.innerHTML = fontRule;
      doc.head.appendChild(style);

      // Select our custom theme
      this.rendition.themes.select('custom-size');
    }
  }

  changeTheme(e: Event) {
    const target = e.target as HTMLSelectElement;
    this.rendition.themes.select(target.value)
    this.rendition.display((document.querySelectorAll('.library-content select option')[1] as HTMLOptionElement).getAttribute('ref'))
    this.rendition.display((document.querySelectorAll('.library-content select option')[0] as HTMLOptionElement).getAttribute('ref'))
  }

  changeFont(e: Event) {
    const target = e.target as HTMLSelectElement;
    this.rendition.themes.override("font-family", target.value);
    //this.rendition.themes.font(`${target.value}!important`)
  }

  showQuoteContextMenu(e: MouseEvent) {
    // const selection = window.parent.document.querySelector('iframe')!.contentWindow!.getSelection
    // const offsetTop = (window.parent.document.getElementById('epub')!.offsetParent as HTMLElement).offsetTop + 93 + e.pageY;
    // (document.querySelector('.library-context') as HTMLDivElement).style.left = `${e.screenX}px`;
    // (document.querySelector('.library-context') as HTMLDivElement).style.top = `${offsetTop}px`;
    // (document.querySelector('.library-context') as HTMLDivElement).style.display = `block`
    // if(selection()) {
    //   this.selection.set(selection()!.toString())
    //   this.quote = selection()!.toString()
    // }
    return false
  }

  addQuoteToFavourites() {
    if(!this.id || !this.quote) return
    this.hideContextMenu()
    this.libraryService.addQuoteToFavourites(this.id, this.quote, this.currentPage).subscribe((res: any) => {
      this.quote = null
      navigator.clipboard.writeText(environment.baseUrl + '/ru/library/' + this.code + '?quoteId=' + res).then(() => {
        this.hideContextMenu()
      })
    })
  }

  hideContextMenu() {
    (document.querySelector('.library-context') as HTMLDivElement).style.display = `none`
  }

  copySelection(e: Event) {
    if(isPlatformBrowser(this.platformId)) {
      e.stopPropagation();
      navigator.clipboard.writeText(this.selection()).then(() => {
        alert('Copied to clipboard')
        this.hideContextMenu()
      })
    }
  }

  shareQuote(e: Event) {
    if(isPlatformBrowser(this.platformId)) {
      e.stopPropagation();
      this.addQuoteToFavourites()
    }
  }

  getTags() {
    return this.data.tags.map((e: any) => e.name).join(', ');
  }

  changeTab(tab: string) {
    this.tab = tab;

    if(tab == 'Читать') {
      if(this.quoteId) {
        this.libraryService.getQuote(this.quoteId).subscribe((res: any) => {
          this.libraryQuote = res;
          this.chapter = res.page;
          this.getChapter(res.page);
        })
      } else {
        // Проверяем сохраненную позицию чтения
        const savedPosition = this.getSavedReadingPosition();
        const startChapter = savedPosition !== null ? savedPosition.chapter : 0;
        this.getChapter(startChapter, savedPosition?.scrollPosition);
      }
    }
  }

  getChapter(index: number, scrollPosition?: number) {
    if(this.data.paid && index > 4 && !this.hasAccess()) {
      return this.toasterService.showToast('Платный материал, оформите подписку для прослушивания всех аудио', 'error', 'bottom-middle', 3000);
    }

    if((this.data.priceRub || this.data.priceEur) && !this.isPurchased() && index > 4) {
      return this.toasterService.showToast('Платный материал, необходимо купить книгу для прослушивания всех аудио', 'error', 'bottom-middle', 3000);
    }

    this.chapter = index; // Обновляем текущую главу

    this.libraryService.getChapter(this.data.id, index).subscribe({
      next: (res: any) => {

        this.chapterContent = res.content.content;
        this.chapterCount = res.count;
        this.chapterTitles = res.titles;

        // Рассчитываем время чтения для текущей главы
        this.chapterReadingTime = this.calculateReadingTime(this.chapterContent);

        // Сохраняем позицию чтения
        this.saveReadingPosition(index);

        if(this.libraryQuote && this.libraryQuote.page == index) {
          this.chapterContent = this.chapterContent.replace(this.libraryQuote.quote.trim(), `<span class="quote" style="background: var(--selection); color: black;">${this.libraryQuote.quote.trim()}</span>`)

          setTimeout(() => {
            const element: HTMLElement = document.querySelector('.quote')!;
            if(element) {
              window.scrollTo({
                top: element.offsetTop + 100,
                behavior: 'smooth'
              });
            }
          }, 500)

        } else if (scrollPosition !== undefined && scrollPosition > 0) {
          // Восстанавливаем позицию скролла если она была сохранена
          setTimeout(() => {
            if (isPlatformBrowser(this.platformId)) {
              window.scrollTo({
                top: scrollPosition,
                behavior: 'smooth'
              });
            }
          }, 500);
        }
      },
      error: err => {
        this.toasterService.showToast(err.error.message, 'error', 'bottom-middle', 5000);
      }
    })

  }

  // Метод для расчета времени чтения
  calculateReadingTime(htmlContent: string): number {
    if (!htmlContent) return 0;

    // Удаляем HTML теги и получаем чистый текст
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    const textContent = tempDiv.textContent || tempDiv.innerText || '';

    // Считаем слова
    const words = textContent.trim().split(/\s+/).filter(word => word.length > 0).length;

    // Средняя скорость чтения 225 слов в минуту
    const wordsPerMinute = 225;
    return Math.ceil(words / wordsPerMinute);
  }

  // Получение ключа localStorage для позиций чтения с учетом пользователя
  private getReadingPositionsKey(): string {
    const userId = this.profileService.profile?.id;
    return userId ? `readingPositions_${userId}` : 'readingPositions_guest';
  }

  // Получение ключа localStorage для настроек fullscreen с учетом пользователя
  private getFullscreenSettingsKey(): string {
    const userId = this.profileService.profile?.id;
    return userId ? `fullscreenSettings_${userId}` : 'fullscreenSettings_guest';
  }

  // Сохранение позиции чтения в localStorage
  private saveReadingPosition(chapter: number): void {
    if (isPlatformBrowser(this.platformId) && this.data?.id) {
      try {
        const storageKey = this.getReadingPositionsKey();
        const readingPositions = JSON.parse(localStorage.getItem(storageKey) || '{}');

        // Очищаем старые позиции (старше 30 дней)
        this.cleanOldReadingPositions(readingPositions);

        readingPositions[this.data.id] = {
          chapter: chapter,
          scrollPosition: window.scrollY,
          timestamp: Date.now(),
          bookCode: this.bookId
        };
        localStorage.setItem(storageKey, JSON.stringify(readingPositions));
      } catch (error) {
        console.warn('Failed to save reading position to localStorage:', error);
      }
    }
  }

  // Получение сохраненной позиции чтения
  private getSavedReadingPosition(): { chapter: number; scrollPosition: number } | null {
    if (isPlatformBrowser(this.platformId) && this.data?.id) {
      try {
        const storageKey = this.getReadingPositionsKey();
        const readingPositions = JSON.parse(localStorage.getItem(storageKey) || '{}');
        const savedPosition = readingPositions[this.data.id];
        if (savedPosition && savedPosition.bookCode === this.bookId) {
          // Показываем тостер о восстановлении позиции
          if (savedPosition.chapter > 0) {
            this.toasterService.showToast(
              `Возвращено к последней позиции чтения (страница ${savedPosition.chapter + 1})`,
              'success',
              'bottom-middle',
              3000
            );
          }
          return {
            chapter: savedPosition.chapter,
            scrollPosition: savedPosition.scrollPosition || 0
          };
        }
      } catch (error) {
        console.warn('Failed to load reading position from localStorage:', error);
      }
    }
    return null;
  }

  // Очистка старых позиций чтения (старше 30 дней)
  private cleanOldReadingPositions(readingPositions: any): void {
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);

    Object.keys(readingPositions).forEach(bookId => {
      const position = readingPositions[bookId];
      if (position.timestamp && position.timestamp < thirtyDaysAgo) {
        delete readingPositions[bookId];
      }
    });
  }

  // Сохранение настроек fullscreen в localStorage
  saveFullscreenSettings(): void {
    if (isPlatformBrowser(this.platformId)) {
      try {
        const storageKey = this.getFullscreenSettingsKey();
        const fullscreenSettings = JSON.parse(localStorage.getItem(storageKey) || '{}');

        // Очищаем старые настройки (старше 90 дней)
        this.cleanOldFullscreenSettings(fullscreenSettings);

        const settingsToSave = {
          theme: this.fullscreenBookTheme,
          font: this.fullscreenBookFont,
          fontSize: this.fullscreenBookFontSize,
          textWidth: this.fullscreenBookTextWidth,
          timestamp: Date.now()
        };

        fullscreenSettings.current = settingsToSave;

        console.log('Saving fullscreen settings:', {
          storageKey,
          settings: settingsToSave,
          userId: this.profileService.profile?.id
        });

        localStorage.setItem(storageKey, JSON.stringify(fullscreenSettings));
      } catch (error) {
        console.warn('Failed to save fullscreen settings to localStorage:', error);
      }
    }
  }

  // Загрузка сохраненных настроек fullscreen
  private loadFullscreenSettings(): void {
    if (isPlatformBrowser(this.platformId)) {
      try {
        const storageKey = this.getFullscreenSettingsKey();
        const fullscreenSettings = JSON.parse(localStorage.getItem(storageKey) || '{}');
        const savedSettings = fullscreenSettings.current;

        console.log('Loading fullscreen settings:', {
          storageKey,
          savedSettings,
          userId: this.profileService.profile?.id
        });

        if (savedSettings) {
          this.fullscreenBookTheme = savedSettings.theme || 'light';
          this.fullscreenBookFont = savedSettings.font || 'Prata';
          this.fullscreenBookFontSize = savedSettings.fontSize || this.getResponsiveDefaultFontSize();
          this.fullscreenBookTextWidth = savedSettings.textWidth || 'narrow';

          console.log('Applied fullscreen settings:', {
            theme: this.fullscreenBookTheme,
            font: this.fullscreenBookFont,
            fontSize: this.fullscreenBookFontSize,
            textWidth: this.fullscreenBookTextWidth
          });
        }
      } catch (error) {
        console.warn('Failed to load fullscreen settings from localStorage:', error);
      }
    }
  }

  // Очистка старых настроек fullscreen (старше 90 дней)
  private cleanOldFullscreenSettings(fullscreenSettings: any): void {
    const ninetyDaysAgo = Date.now() - (90 * 24 * 60 * 60 * 1000);

    if (fullscreenSettings.current && fullscreenSettings.current.timestamp && fullscreenSettings.current.timestamp < ninetyDaysAgo) {
      delete fullscreenSettings.current;
    }
  }

  getAudioDuration(url: string): Promise<number> {
    return new Promise((resolve, reject) => {
      const audio = new Audio(url);
      audio.addEventListener("loadedmetadata", () => {
        resolve(audio.duration);
      });
      audio.addEventListener("error", (e) => {
        reject(`Error loading audio: ${e}`);
      });
    });
  }

  play(items: any) {
    if(this.data.paid && items[0].index > 0 && !this.hasAccess()) {
      return this.toasterService.showToast('Платный материал, оформите подписку для прослушивания всех аудио', 'error', 'bottom-middle', 3000);
    }

    if((this.data.priceRub || this.data.priceEur) && !this.isPurchased() && items[0].index > 0) {
      return this.toasterService.showToast('Платный материал, необходимо купить книгу для прослушивания всех аудио', 'error', 'bottom-middle', 3000);
    }

    let format = items.map(({ chapter, url, time }: ChapterData) => ({
      link: url,
      title: chapter,
      time
    }));

    this.shareDataService.changePlaylist([...format])
  }

  isPurchased() {
    if(!this.profileService.profile) return false;

    return this.profileService.profile!.libraryPurchases.some((e: any) => e.id == this.data.id);
  }

  hasAccess() {
    if(!this.profileService.profile) return false;

    return this.profileService.profile.subscriptions.some((e: any) => ['LIBRARY', 'AUDIO_AND_LIBRARY', 'FULL_ACCESS'].includes(e.type))
  }

  transform(html: string) {
    return this.sanitizer.bypassSecurityTrustHtml(html);
  }

  prev() {
    if (this.chapter > 0) {
      // Сохраняем текущую позицию скролла перед переходом
      this.saveReadingPosition(this.chapter);
      this.chapterContent = ''; // Clear content to show loading
      this.getChapter(this.chapter - 1);
      this.scrollToTop();
    }
  }

  next() {
    if (this.chapter < this.chapterCount - 1) {
      if(this.chapter >= 3) {
        if(this.checkPurchase()) {
          this.toasterService.showToast('Вы достигли лимита в 5 страниц для бесплатного просмотра книги. Чтобы продолжить чтение, пожалуйста, купите книгу.', 'error', 'bottom-middle', 5000)
          return;
        }

        if(this.checkSubscription()) {
          this.toasterService.showToast('Вы достигли лимита в 5 страниц для бесплатного просмотра книги. Чтобы продолжить чтение, пожалуйста, оформите подписку.', 'error', 'bottom-middle', 5000)
          return;
        }
      }

      // Сохраняем текущую позицию скролла перед переходом
      this.saveReadingPosition(this.chapter);
      this.chapterContent = ''; // Clear content to show loading
      this.getChapter(this.chapter + 1);
      this.scrollToTop();
    }
  }

  checkPurchase() {
    return (this.data.priceEur || this.data.priceRub) && !this.data.isPurchased;
  }

  checkSubscription() {
    return this.data.paid && !this.libraryService.hasAccess();
  }

  scrollToTop() {
    if (isPlatformBrowser(this.platformId)) {
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: 'smooth'
      });

    }
  }

  showFullAnnotation: boolean = false;

  toggleAnnotationDisplay() {
    this.showFullAnnotation = !this.showFullAnnotation;
  }

  isInFavourites(id: number) {
    return this.favouriteContent?.includes(id)
  }

  isLiked(id: number) {
    return this.likesContent?.includes(id)
  }

  showBookElement(elementType: string): void {
    this.relatedBooksShowLast = elementType === 'last';
    console.log(this.relatedBooksShowLast, 'this.relatedBooksShowLast');

  }

  openUrl(link: string) {
    if (isPlatformBrowser(this.platformId)) {
      window.open(link);
    }
  }

  openBook(slug: string, event?: MouseEvent) {
    // Если передан event, проверяем на Ctrl+Click для открытия в новой вкладке
    if (event) {
      if (event.ctrlKey || event.metaKey || event.button === 1 || event.shiftKey) {
        event.preventDefault();
        event.stopPropagation();
        const url = `/${this.translocoService.getActiveLang()}/library/${slug}`;
        if (isPlatformBrowser(this.platformId)) {
          window.open(url, '_blank');
        }
        return;
      }

      // Для обычного клика предотвращаем стандартное поведение
      event.preventDefault();
      event.stopPropagation();
    }

    // SPA навигация с прокруткой вверх и обновлением данных
    this.router.navigate([`${this.translocoService.getActiveLang()}/library/${slug}`]).then((success) => {
      if (success && isPlatformBrowser(this.platformId)) {
        // Прокручиваем к началу страницы
        window.scrollTo({ top: 0, behavior: 'smooth' });

        // Сбрасываем данные похожих книг для обновления
        this.similar = [];
        this.similarCall = false;
      }
    });
  }

  selectChapter(index: any) {
    this.chapter = index;
  }

  secondsToHMS(seconds: any) {
    const numSeconds = Number(seconds);

    // Проверяем на валидность числа
    if (isNaN(numSeconds) || numSeconds < 0) {
      return '00:00:00';
    }

    const hours = Math.floor(numSeconds / 3600);
    const minutes = Math.floor((numSeconds % 3600) / 60);
    const secs = Math.floor(numSeconds % 60);

    const pad = (num: number) => num.toString().padStart(2, '0');

    return `${pad(hours)}:${pad(minutes)}:${pad(secs)}`;
  }

  // Метод для обновления массива табов в зависимости от наличия аудио
  updateTabs() {
    const baseTabs = ['О книге', 'Читать'];
    if (this.hasAudio) {
      this.tabs = [...baseTabs, 'Аудиокнига'];
    } else {
      this.tabs = baseTabs;
      // Если текущий таб - Аудиокнига, переключаемся на О книге
      if (this.tab === 'Аудиокнига') {
        this.tab = 'О книге';
      }
    }
  }

  // Геттер для проверки наличия аудио
  get hasAudio(): boolean {
    return this.data &&
           this.data.audio &&
           Array.isArray(this.data.audio) &&
           this.data.audio.length > 0;
  }

  navigateToTaggedLibrary(tagId: number) {
    const lang = this.translocoService.getActiveLang();
    this.router.navigate([`/${lang}/library`], {
      queryParams: { tags: tagId }
    });
  }

  playList(items: Track[]) {
    items = items.map((e: any) => ({...e, link: e.url}))
    this.shareDataService.changePlaylist([...items])
  }

  // Add to existing class properties
  @ViewChild('fullscreenDialog') fullscreenDialog!: ElementRef<HTMLDialogElement>;

  // Add this method to open the dialog
  openFullscreenDialog() {
    if (isPlatformBrowser(this.platformId)) {
      this.isFullscreenBookDialogActive = true;
      this.fullscreenDialog.nativeElement.showModal();



      // Ensure we have chapter content
      if (!this.chapterContent) {
        this.getChapter(this.chapter);
      }

    }
  }

  // Add this method to close the dialog
  closeFullscreenDialog() {
    this.isFullscreenBookDialogActive = false;
    this.fullscreenDialog.nativeElement.close();
  }











  showScrollTop: boolean = false;
  similarCall: boolean = false;
  private scrollSaveTimeout: any = null;

  @HostListener('window:scroll', [])
  onWindowScroll() {
    if (isPlatformBrowser(this.platformId)) {
      this.showScrollTop = window.scrollY > 300;
      if(this.showScrollTop){

          if(this.similar.length === 0 && !this.similarCall) {
            this.similarCall = true;
            this.libraryService.getSimilar(this.bookId).subscribe((res: any) => {
              this.similar = res;
            });
          }
      }

      // Сохраняем позицию скролла с задержкой (debounce)
      if (this.tab === 'Читать' && this.chapterContent) {
        if (this.scrollSaveTimeout) {
          clearTimeout(this.scrollSaveTimeout);
        }
        this.scrollSaveTimeout = setTimeout(() => {
          this.saveReadingPosition(this.chapter);
        }, 1000); // Сохраняем через 1 секунду после остановки скролла
      }
    }
  }

  moveLeft() {
    if (isPlatformBrowser(this.platformId)) {
      document.getElementById('carousel')?.scrollBy({
        left: -260,
        behavior: 'smooth'
      });
    }
  }

  moveRight() {
    if (isPlatformBrowser(this.platformId)) {
      document.getElementById('carousel')?.scrollBy({
        left: 260,
        behavior: 'smooth'
      });
    }
  }

  // Navigation methods for fullscreen mode
  nextFullscreen() {
    if (this.chapter < this.chapterCount - 1) {
      this.chapterContent = ''; // Clear content to show loading
      this.getChapter(this.chapter + 1);
      this.scrollToTopFullscreen();
    }
  }

  prevFullscreen() {
    if (this.chapter > 0) {
      this.chapterContent = ''; // Clear content to show loading
      this.getChapter(this.chapter - 1);
      this.scrollToTopFullscreen();
    }
  }

  goToChapterFullscreen(chapterIndex: number) {
    this.chapterContent = ''; // Clear content to show loading
    this.getChapter(chapterIndex);
    this.fullscreenContents = false; // Close contents after selection
    this.scrollToTopFullscreen();
  }

  scrollToTopFullscreen() {
    if (isPlatformBrowser(this.platformId)) {
      // Scroll to top of the fullscreen dialog content
      const dialogContent = this.fullscreenDialog.nativeElement.querySelector('.book-pages-container');
      if (dialogContent) {
        dialogContent.scrollTo({
          top: 0,
          left: 0,
          behavior: 'smooth'
        });
      }
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    if (isPlatformBrowser(this.platformId)) {
      const target = event.target as HTMLElement;
      const settingsContainer = document.querySelector('.settings-container');

      // Если клик вне settings-container, закрываем меню
      if (settingsContainer && !settingsContainer.contains(target)) {
        this.showFullscreenOptions = false;
      }
    }
  }

  // Метод для получения стилей размера шрифта
  getFullscreenFontSizeStyles() {
    const lineHeight = this.calculateProportionalLineHeight(this.fullscreenBookFontSize);
    return {
      'font-size': this.fullscreenBookFontSize + 'px',
      'line-height': lineHeight + 'px',
      '--fullscreen-font-size': this.fullscreenBookFontSize + 'px',
      '--fullscreen-line-height': lineHeight + 'px'
    };
  }

  // Метод для получения стилей ширины текста
  getFullscreenTextWidthStyles() {
    const widthMap = {
      'narrow': '700px',
      'medium': '930px',
      'full': '100%'
    };
    return {
      'max-width': widthMap[this.fullscreenBookTextWidth as keyof typeof widthMap],
      'margin': '0 auto'
    };
  }

  // Метод для применения стилей через JavaScript
  applyFullscreenFontSize() {
    if (isPlatformBrowser(this.platformId)) {
      setTimeout(() => {
        const lineHeight = this.calculateProportionalLineHeight(this.fullscreenBookFontSize);

        // Находим все элементы текста внутри text-interaction
        const textElements = document.querySelectorAll('.fullscreen-book-dialog text-interaction *');

        textElements.forEach((element: any) => {
          // Сохраняем текущий font-weight перед изменением размера
          const currentFontWeight = window.getComputedStyle(element).fontWeight;
          element.style.fontSize = this.fullscreenBookFontSize + 'px';
          element.style.lineHeight = lineHeight + 'px';
          element.style.setProperty('font-size', this.fullscreenBookFontSize + 'px', 'important');
          element.style.setProperty('line-height', lineHeight + 'px', 'important');
          // Восстанавливаем font-weight
          if (currentFontWeight && currentFontWeight !== 'normal') {
            element.style.setProperty('font-weight', currentFontWeight, 'important');
          }
        });

        // Также применяем к самому text-interaction
        const textInteraction = document.querySelector('.fullscreen-book-dialog text-interaction');
        if (textInteraction) {
          const currentFontWeight = window.getComputedStyle(textInteraction).fontWeight;
          (textInteraction as HTMLElement).style.fontSize = this.fullscreenBookFontSize + 'px';
          (textInteraction as HTMLElement).style.lineHeight = lineHeight + 'px';
          (textInteraction as HTMLElement).style.setProperty('font-size', this.fullscreenBookFontSize + 'px', 'important');
          (textInteraction as HTMLElement).style.setProperty('line-height', lineHeight + 'px', 'important');
          if (currentFontWeight && currentFontWeight !== 'normal') {
            (textInteraction as HTMLElement).style.setProperty('font-weight', currentFontWeight, 'important');
          }
        }
      }, 500);

      // Сохраняем настройки после применения изменений
      this.saveFullscreenSettings();
    }
  }

  openPurchaseModal() {
    if(!this.profileService.profile) {
      this.toasterService.showToast('Необходимо авторизоваться', 'error', 'bottom-middle');
      return;
    }
    this.showPurchaseModal = true;
  }

  closePurchaseModal() {
    this.showPurchaseModal = false;
  }

  onPurchaseComplete() {
    this.showPurchaseModal = false;
    // Обновляем данные книги после покупки
    this.getDate(false);
  }


}

interface ChapterData {
  chapter: string;
  url: string;
  time: number
}
