import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, Output, ViewChild, signal } from '@angular/core';

export interface DialogButton {
  text: string;
  class: string;
  action: 'confirm' | 'cancel' | 'custom';
  customAction?: string;
}

export type DialogType = 'alert' | 'confirm' | 'custom';

@Component({
  selector: 'admin-dialog',
  standalone: true,
  imports: [CommonModule],
  template: `
    <dialog #dialog class="admin-modal" (click)="onBackdropClick($event)">
      <div class="admin-modal-content" (click)="$event.stopPropagation()">
        @if (showHeader()) {
          <div class="admin-modal-header">
            <h3 class="admin-modal-title">{{ title() }}</h3>
            @if (showCloseButton()) {
              <button class="admin-modal-close" (click)="closeDialog()">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            }
          </div>
        }
        <div class="admin-modal-body">
          {{ message() }}
        </div>
        <div class="admin-modal-footer">
          @for (button of buttons(); track button.text) {
            <button
              [class]="'btn ' + button.class"
              (click)="handleButtonClick(button)">
              {{ button.text }}
            </button>
          }
        </div>
      </div>
    </dialog>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AdminDialogComponent {
  @ViewChild('dialog') dialog!: ElementRef<HTMLDialogElement>;
  
  // Signals for reactive state management
  message = signal<string>('');
  title = signal<string>('');
  dialogType = signal<DialogType>('alert');
  buttons = signal<DialogButton[]>([]);
  showHeader = signal<boolean>(false);
  showCloseButton = signal<boolean>(false);
  
  @Output() confirmed = new EventEmitter<void>();
  @Output() cancelled = new EventEmitter<void>();
  @Output() closed = new EventEmitter<void>();
  @Output() customAction = new EventEmitter<string>();

  // Method to show alert dialog
  showAlert(message: string, title?: string): void {
    this.message.set(message);
    this.title.set(title || 'Уведомление');
    this.dialogType.set('alert');
    this.showHeader.set(!!title);
    this.showCloseButton.set(false);
    this.buttons.set([
      { text: 'OK', class: 'btn-primary', action: 'confirm' }
    ]);
    this.dialog.nativeElement.showModal();
  }

  // Method to show confirm dialog
  showConfirm(message: string, title?: string): Promise<boolean> {
    this.message.set(message);
    this.title.set(title || 'Подтверждение');
    this.dialogType.set('confirm');
    this.showHeader.set(!!title);
    this.showCloseButton.set(false);
    this.buttons.set([
      { text: 'Да', class: 'btn-danger', action: 'confirm' },
      { text: 'Отмена', class: 'btn-outline-secondary', action: 'cancel' }
    ]);
    this.dialog.nativeElement.showModal();

    return new Promise((resolve) => {
      const confirmSub = this.confirmed.subscribe(() => {
        confirmSub.unsubscribe();
        cancelSub.unsubscribe();
        resolve(true);
      });

      const cancelSub = this.cancelled.subscribe(() => {
        confirmSub.unsubscribe();
        cancelSub.unsubscribe();
        resolve(false);
      });
    });
  }

  // Method to show custom dialog
  showCustom(message: string, customButtons: DialogButton[], title?: string, showClose?: boolean): void {
    this.message.set(message);
    this.title.set(title || '');
    this.dialogType.set('custom');
    this.showHeader.set(!!title);
    this.showCloseButton.set(showClose || false);
    this.buttons.set(customButtons);
    this.dialog.nativeElement.showModal();
  }

  // Handle button clicks
  handleButtonClick(button: DialogButton): void {
    switch (button.action) {
      case 'confirm':
        this.confirmed.emit();
        break;
      case 'cancel':
        this.cancelled.emit();
        break;
      case 'custom':
        if (button.customAction) {
          this.customAction.emit(button.customAction);
        }
        break;
    }
    this.closeDialog();
  }

  // Handle backdrop click
  onBackdropClick(event: MouseEvent): void {
    if (event.target === this.dialog.nativeElement) {
      this.closeDialog();
    }
  }

  // Close dialog
  closeDialog(): void {
    this.dialog.nativeElement.close();
    this.closed.emit();
  }
}
