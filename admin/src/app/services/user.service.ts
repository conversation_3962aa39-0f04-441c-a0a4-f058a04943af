import { HttpClient } from "@angular/common/http"
import { Injectable, inject, signal } from '@angular/core'
import { tap } from "rxjs"

export enum GroupsEnum {
  ADMIN = 'Администраторы сайта',
  FORUM_ADMIN = 'Администраторы форума',
  VISITOR = 'Посетители',
  STUDENT = 'Ученики',
  PERSONAL_SITE = 'Личные сайты',
  STUDENT_LIST = 'Работа со списком учеников',
  NEW = 'Новые анкеты',

  PAGE_MODERATOR = 'Модератор страниц',
  TRANSLATION_MODERATOR = 'Модератор переводов',
  LIBRARY_MODERATOR = 'Модератор книг',
  PHOTO_MODERATOR = 'Модератор фото',
  LECTURE_MODERATOR = 'Модератор лекций',
  CONSTRUCTOR_MODERATOR = 'Модератор конструктора',
  USER_MODERATOR = 'Модератор пользователей',
  PERSONALPAGE_MODERATOR = 'Модератор личных страниц',
  FORUM_MODERATOR = 'Модератор форума',
  CALENDAR_MODERATOR = 'Модератор календаря',
  AUDIO_MODERATOR = 'Модератор аудио',
}

@Injectable({
  providedIn: 'root'
})
export class UserService {
    http = inject(HttpClient)
    name = signal<string | null>(null)
    users: any = []
    groups = signal<GroupsEnum[]>([]);
    profile: any = null

    getOne(id: number) {
      return this.http.get('/user/' + id)
    }

    getAll(page: number) {
      return this.http.get('/user', {params: {page}}).pipe(
        tap(res => this.users = res),
      )
    }

    getProfile() {
        return this.http.get('/user/profile').pipe(
            tap((res: any) => {
              this.groups.set(res.groups || []);
              this.name.set(res.firstName)
              this.profile = res;
            })
        )
    }

    getGroups() {
      return this.http.get('/user/groups')
    }

    getStatuses() {
      return this.http.get('/user/statuses')
    }

    updateUser(data: any) {
      return this.http.patch(`/user/profile`, data)
    }

    deleteUser(id: number) {
      return this.http.delete(`/user/${id}`)
    }

    addSubscription(userId: number, data: any) {
      return this.http.post(`/admin/subscriptions/user/${userId}`, data)
    }

    removeSubscription(subscriptionId: number) {
      return this.http.delete(`/admin/subscriptions/${subscriptionId}`)
    }

    getSubscriptionTypes() {
      return [
        { value: 'AUDIO', label: 'Аудио' },
        { value: 'LIBRARY', label: 'Библиотека' },
        { value: 'COURSES', label: 'Курсы' },
        { value: 'AUDIO_AND_LIBRARY', label: 'Аудио + Библиотека' },
        { value: 'AUDIO_AND_COURSES', label: 'Аудио + Курсы' },
        { value: 'LIBRARY_AND_COURSES', label: 'Библиотека + Курсы' },
        { value: 'AI', label: 'AI-чат' },
        { value: 'FULL_ACCESS', label: 'Полный доступ' }
      ]
    }

  hasGroup(groups: string | string[]): boolean {
    const userGroups: any = this.groups();

    if (userGroups.includes('ADMIN')) {
      return true;
    }

    const groupsToCheck = Array.isArray(groups) ? groups : [groups];

    return groupsToCheck.some(role => userGroups.includes(role));
  }
}
