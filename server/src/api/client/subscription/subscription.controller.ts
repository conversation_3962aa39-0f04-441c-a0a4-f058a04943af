import { JwtAuthGuard } from '@/api/user/guards/auth.guard'
import { Body, Controller, Get, HttpCode, Post, Req, UseGuards } from '@nestjs/common'
import { SubscriptionService } from './subscription.service'

@Controller('client/subscriptions')
export class SubscriptionController {
  constructor(private readonly subscriptionService: SubscriptionService) {}

  @Get()
  async getList() {
    return this.subscriptionService.getList();
  }

  @Get('test')
  async getPaymentMethod() {
    return (await this.subscriptionService.getPaymentMethod('pm_1S7vxn1ElDUyO74h7hHFDfVD')).card.last4;
  }

  @Post('pay')
  @UseGuards(JwtAuthGuard)
  async paySubscription(@Req() req, @Body() body: any) {
    return await this.subscriptionService.pay(body, req.user)
  }

  @Post('paid')
  @HttpCode(200)
  async onSubscriptionPaid(@Body() body: any) {
    return await this.subscriptionService.onSubscriptionPaid(body);
  }

  @Post('cancel-auto-renew')
  @UseGuards(JwtAuthGuard)
  async cancelAutoRenew(@Body() body: { subscriptionId: number }, @Req() req) {
    return this.subscriptionService.cancelAutoRenew(body.subscriptionId, req.user);
  }

}
